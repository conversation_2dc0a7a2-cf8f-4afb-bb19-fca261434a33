# Default values for common-java.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  registry: registry.cn-beijing.aliyuncs.com
  registryNamespace: ijx 
  repository: nginx 
  # Always IfNotPresent
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: "latest" # 镜像版本 如 1.2.3
  # args: ["-XX:PrintGC", "/usr/local/jetty/start.jar"]
  # command: ["java"]
  # args:  ["-XX:PrintGC", "/usr/local/jetty/start.jar"]

port: 8080
portProtocol: TCP
svcNameOverride: ""
nodePortSvc:
  enabled: false
  protocol: TCP
  port: 8080
  targetPort: 8080
  nodePort: 38080

apollo:
  enabled: true
  name: apollo-uat-config

env:
  - name: SPRING_PROFILES_ACTIVE
    value: UAT

startupProbe:
  enabled: false
  path: /actuator/health
  initialDelaySeconds: 30

livenessProbe:
  enabled: false
  path: /actuator/health
  initialDelaySeconds: 30

readinessProbe:
  enabled: false
  path: /actuator/health/
  initialDelaySeconds: 30


# imagePullSecrets: []
imagePullSecrets: [{name: aliyun-secret},{name: huaweiyun-secret}]
#nameOverride: "edc-monitor"
#nameOverride: "edc-monitor"
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations:
  fluentbit.io/parser: java_parser

podLabels:
  apptype: java
  
podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP

ingress:
  enabled: false
  className: ""
  tlsAnnotations: 
    certmanager.k8s.io/cluster-issuer: letsencrypt-with-http
    cert-manager.io/cluster-issuer: letsencrypt-with-http
    kubernetes.io/ingress.class: traefik
    traefik.ingress.kubernetes.io/router.tls: "true"
  annotations: 
    # kubernetes.io/ingress.class: traefik
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: common-java.uat.967111.net
      paths:
        - path: /
          pathType: Prefix
  tls: []
  #  - secretName: common-java-uat-967111-net
  #    hosts:
  #      - common-java.uat.967111.net

#resources: {}
pvcs: []
# pvcs:
#   - name: abcd
#     storage: 1Gi
#     storageClassName: nfs-client
#     mountPath: /tmp/abcd/
configMaps: []
# configMaps:
#   - name: configMapName
#     mountPath: /tmp/abcd/

    # key: []
    # subPath: config.json
    
resources: 
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 500m
    memory: 512Mi
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}
tolerations: []

affinity: {}

serviceMonitor:
  enabled: false
  port: http
  path: /actuator/prometheus
  interval: 5s 

aliyunLog:
  enabled: false
  lifeCycle: 7
  beginLineEnabled: true
  beginLineCheckLength: 10
  beginLineRegex: "\\[(.*)\\s].*"
  regex: '\[(.*)\s] (\d+-\d+-\d+ \d+:\d+:\d+) (\[.+]) - ([\s\S]*)'
  keys: ['level', 'time', 'method', 'message']