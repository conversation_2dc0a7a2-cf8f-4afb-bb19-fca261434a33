apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "common-java.fullname" . }}
  labels:
    {{- include "common-java.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "common-java.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- include "common-java.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "common-java.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      volumes:
      {{- if .Values.apollo.enabled }}
      - name: apollo-volume
        configMap:
          name: {{ .Values.apollo.name | default "apollo-uat-config"}}
      {{- end }}
      {{- range .Values.configMaps }}
      - name: configmap-{{.name}}-volume
        configMap:
          name: {{ .name}}
      {{- end }}
      {{- range .Values.pvcs }}
      - name: pvc-{{.name}}-volume
        persistentVolumeClaim:
          claimName: {{ $.Release.Name }}-{{ .name }}
      {{- end }}
      {{- range .Values.sharedVolumes }}
      - name: {{ .name }}
        emptyDir: {}
      {{- end }}
      {{- if .Values.initContainers }}
      initContainers:
      {{- range .Values.initContainers }}
      - name: {{ .name }}
        image: {{ .image }}
        {{- if .command }}
        command:
        {{- toYaml .command | nindent 10 }}
        {{- end }}
        {{- if .volumeMounts }}
        volumeMounts:
        {{- toYaml .volumeMounts | nindent 10 }}
        {{- end }}
      {{- end }}
      {{- end }}
      containers:
        # - name: {{ .Chart.Name }}
        # - name: {{ .Release.Name | replace "_" "-" | trunc 63 | trimSuffix "-" }}
        - name: {{ .Release.Name | trunc 63 | trimSuffix "-" }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.registry }}/{{ .Values.image.registryNamespace}}/{{ .Values.image.repository }}:{{ .Values.image.tag | default "latest" }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- if and .Values.image.args .Values.image.command }}
          command:
          {{- toYaml .Values.image.command | nindent 12 }}
          args:
          {{- toYaml .Values.image.args | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.port | default 80 }}
              protocol: {{ .Values.portProtocol | default "TCP" }}
          env:
            - name: JAEGER_AGENT_HOST
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
          {{- range .Values.env }}
            - name: {{ .name| quote }}
              value: {{ .value | quote }}
          {{- end }}
          volumeMounts:
          {{- if .Values.apollo.enabled }}
          - mountPath: /opt/settings
            name: apollo-volume
          {{- end }}
          {{- range .Values.configMaps }}
          - name: configmap-{{.name}}-volume
            mountPath: {{ .mountPath }}
          {{- end }}
          {{- range .Values.pvcs }}
          - name: pvc-{{.name}}-volume
            mountPath: {{ .mountPath }}
          {{- end }}
          {{- range .Values.sharedVolumes }}
          - name: {{ .name }}
            mountPath: {{ .mountPath }}
          {{- end }}
          {{- if .Values.startupProbe.enabled }}
          startupProbe:
            httpGet:
              path: {{ .Values.startupProbe.path | default "/"}}
              port: http
            initialDelaySeconds: {{ .Values.startupProbe.initialDelaySeconds}}
          {{- end }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.livenessProbe.path | default "/"}}
              port: http
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds}}
          {{- end }}
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: {{ .Values.readinessProbe.path | default "/"}}
              port: http
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds}}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
