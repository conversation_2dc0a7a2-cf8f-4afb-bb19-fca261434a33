---
apiVersion: v1
kind: Service
metadata:
{{- if .Values.svcNameOverride }}
  name: {{ .Values.svcNameOverride }}
{{- else }}
  name: {{ include "common-java.fullname" . }}
{{- end }}
  labels:
    {{- include "common-java.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.port | default 80}}
      targetPort: {{ .Values.port | default 80}}
      protocol: {{ .Values.portProtocol | default "TCP" }}
      name: http
  selector:
    {{- include "common-java.selectorLabels" . | nindent 4 }}

---
{{- if .Values.nodePortSvc.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "common-java.fullname" . }}-nodeport
  labels:
    {{- include "common-java.labels" . | nindent 4 }}
spec:
  type: NodePort
  ports:
    - port: {{ .Values.nodePortSvc.port | default 8080 }}
      targetPort: {{ .Values.nodePortSvc.targetPort | default 8080 }}
      protocol: {{ .Values.nodePortSvc.protocol | default "TCP" }}
      nodePort: {{ .Values.nodePortSvc.nodePort }}
  selector:
    {{- include "common-java.selectorLabels" . | nindent 4 }}
{{- end }}
