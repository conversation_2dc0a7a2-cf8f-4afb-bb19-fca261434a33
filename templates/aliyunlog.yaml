---
{{- if .Values.aliyunLog.enabled -}}
apiVersion: log.alibabacloud.com/v1alpha1
kind: AliyunLogConfig
metadata:
  # your config name, must be unique in you k8s cluster
  # name: ijxadmin-web
  name: {{ include "common-java.fullname" . }}
spec:
  # logstore name to upload log
  logstore: {{ .Release.Name | trunc 63 | trimSuffix "-" }}
  lifeCycle: {{ .Values.aliyunLog.lifeCycle | default 3  }}
  # logtail config detail
  logtailConfig:
    # docker stdout's input type is 'plugin'
    inputType: plugin
    # logtail config name, should be same with [metadata.name]
    configName:  {{ .Release.Name | trunc 63 | trimSuffix "-" }}
    inputDetail:
      plugin:
        inputs:
          -
            # input type
            type: service_docker_stdout
            detail:
              # collect stdout and stderr
              Stdout: true
              Stderr: true
              {{- if .Values.aliyunLog.beginLineEnabled }}
              BeginLineCheckLength: {{.Values.aliyunLog.beginLineCheckLength }}
              BeginLineRegex: {{.Values.aliyunLog.beginLineRegex }}
              {{- end }}
              # collect all container's stdout except containers with "COLLECT_STDOUT_FLAG:false" in docker env config
              IncludeLabel:
              {{- if .Values.aliyunLog.containerNameOverride }}
                io.kubernetes.container.name: {{.Values.aliyunLog.containerNameOverride }}
              {{- else }}
                io.kubernetes.container.name: {{ .Release.Name | trunc 63 | trimSuffix "-" }}
              {{- end }}
        processors:
          -
            # 使用正则表达式处理
            type: processor_regex
            detail:
              SourceKey: content
              Regex: {{.Values.aliyunLog.regex }}
              Keys: {{.Values.aliyunLog.keys | toStrings}}
              KeepSource: true
              NoKeyError: true
              NoMatchError: true
{{- end }} 