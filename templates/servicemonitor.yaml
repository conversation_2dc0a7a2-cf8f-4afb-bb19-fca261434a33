{{- if .Values.serviceMonitor.enabled -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "common-java.fullname" . }}
  labels:
    {{- include "common-java.labels" . | nindent 4 }}
spec:
  namespaceSelector:
    matchNames:
    - {{ .Release.Namespace }}
  selector:
    matchLabels:
      # app.kubernetes.io/name: {{ include "common-java.fullname" . }}
      app: {{ .Release.Name}}
  endpoints:
  - port: {{ .Values.serviceMonitor.port }}
    path: {{ .Values.serviceMonitor.path }}
    interval: {{ .Values.serviceMonitor.interval }}
{{- end }}    
