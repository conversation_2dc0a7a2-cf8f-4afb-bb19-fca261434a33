# 简单 java 项目通用 helm chart 模板

如果项目只需要自定义设置 项目名、镜像名、版本、端口、域名等，那么这个 chart 模板恰好能满足需求。
比如：

- 项目 new-comers
- 镜像 registry.cn-beijing.aliyuncs.com/ijx/newcomers-main:latest
- 端口 8080
- 域名 newcomers.uat.967111.net

则可以直接通过 [运维平台](https://rock.967111.net/) 进行部署,自定义 values 如下

```yaml
image:
  repository: newcomers-main
  tag: latest
ingress:
  hosts:
    - paths:
        - pathType: Prefix
          path: /
      host: newcomers.uat.967111.net
  enabled: true
```

如果连域名也不需要，那更简单。因为默认不创建域名

```yaml
image:
  repository: newcomers-main
  tag: latest
```

## Introduction

## Prerequisites

目前只支持 `移动 IDC 机房` 和 `华为云 *************`

- Kubernetes 1.19+
- Helm 3.7.0+

## 安装 chart

华为云已安装 135884-ijx_uat/common-java
在华为云上k8s集群上，使用 common-java chart 安装 release  `java-demo`

```bash
$ helm repo update # 更新 公司私有 helm 仓库
$ helm install java-demo common-java
```

> **提示**: 可通过 `helm list` 查看所有已安装的 release

## 卸载 chart

卸载 `java-demo` deployment:

```bash
$ helm delete java-demo
```

## 参数


### deployment 参数

| Name                                    | Description                                                                               | Value           |
| --------------------------------------- | ----------------------------------------------------------------------------------------- | --------------- |
| `replicaCount`                          | pod个数                                                        | `1`             |
| `image.registry` | 公司私有docker镜像仓库地址                                      | `registry.cn-beijing.aliyuncs.com`          |
| `image.registryNamespace`                   | 公司私有docker镜像仓库命名空间                                                     | `ijx`          |
| `image.repository`                   | 镜像名字                                                     | `nginx`          |
| `image.tag`                   | 镜像tag                                                     | `latest`          |
| `image.pullPolicy`                  | 镜像拉取策略                         | `Always`            |
| `port`                      | 服务端口                                              | `8080`            |
| `apollo.enabled`                    | 是否挂载 apollo config                                           | `true`            |
| `apollo.name`                    | apollo configmap 名字                                            | `apollo-uat-config`            |
| `livenessProbe.enabled`                 | Enable livenessProbe                                                                      | `true`          |
| `livenessProbe.initialDelaySeconds`     | Initial delay seconds for livenessProbe                                                   | `30`            |
| `livenessProbe.periodSeconds`           | Period seconds for livenessProbe                                                          | `10`            |
| `livenessProbe.timeoutSeconds`          | Timeout seconds for livenessProbe                                                         | `5`             |
| `livenessProbe.failureThreshold`        | Failure threshold for livenessProbe                                                       | `6`             |
| `livenessProbe.successThreshold`        | Success threshold for livenessProbe                                                       | `1`             |
| `readinessProbe.enabled`                | Enable readinessProbe                                                                     | `true`          |
| `readinessProbe.initialDelaySeconds`    | Initial delay seconds for readinessProbe                                                  | `5`             |
| `readinessProbe.periodSeconds`          | Period seconds for readinessProbe                                                         | `5`             |
| `readinessProbe.timeoutSeconds`         | Timeout seconds for readinessProbe                                                        | `3`             |
| `readinessProbe.failureThreshold`       | Failure threshold for readinessProbe                                                      | `3`             |
| `readinessProbe.successThreshold`       | Success threshold for readinessProbe                                                      | `1`             |
| `autoscaling.enabled`                   | Enable autoscaling for NGINX deployment                                                   | `false`         |
| `autoscaling.minReplicas`               | Minimum number of replicas to scale back                                                  | `""`            |
| `autoscaling.maxReplicas`               | Maximum number of replicas to scale out                                                   | `""`            |
| `autoscaling.targetCPU`                 | Target CPU utilization percentage                                                         | `""`            |
| `autoscaling.targetMemory`              | Target Memory utilization percentage                                                      | `""`            |
| `extraVolumes`                          | Array to add extra volumes                                                                | `[]`            |
| `extraVolumeMounts`                     | Array to add extra mount                                                                  | `[]`            |
| `serviceAccount.create`                 | Enable creation of ServiceAccount for nginx pod                                           | `false`         |
| `serviceAccount.name`                   | The name of the ServiceAccount to use.                                                    | `""`            |
| `serviceAccount.annotations`            | Annotations for service account. Evaluated as a template.                                 | `{}`            |
| `serviceAccount.autoMount`              | Auto-mount the service account token in the pod                                           | `false`         |







## 测试 生成的 yaml 文件

```
helm template java-demo .
```
## package helm
```
helm package .
```
## 安装 helm 
```
helm install java-demo . --set image.tag=latest  --set ingress.enabled=false  --dry-run
```

## 获取 安装后的 release 信息

```
helm get values java-demo
helm status java-demo
```

## 升级版本

helm upgrade --install <release name> --values <values file> <chart directory>
```
helm upgrade --install java-demo .  --wait --set image.tag=latest  --set ingress.enabled=false
```

## 查看历史版本

```
helm history java-demo 
```

## 回滚到历史版本

```
helm rollback java-demo 1
```

## 升级命令也可以用来新建release

```
helm upgrade --install  java-demo .  --wait --set image.tag=latest  --set ingress.enabled=false
```

